import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { InvitationService } from './invitation.service';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { CreateInvitationDto } from './dto/create-invitation.dto';
import { AcceptInvitationDto } from './dto/accept-invitation.dto';
import { RejectInvitationDto } from './dto/reject-invitation.dto';
import { SendInvitationEmailDto } from './dto/send-invitation-email.dto';
import { Public } from 'src/auth/decorators/auth.decorator';

@Controller('invitation')
export class InvitationController {
  constructor(private invitationService: InvitationService) {}

  @Roles(['ADMIN'])
  @Post('create')
  async createInvitation(@Body() invitationDto: CreateInvitationDto) {
    return this.invitationService.createInvitation(invitationDto);
  }

  @Roles(['ADMIN'])
  @Post('reject')
  async rejectInvitation(@Body() rejectInvitationDto: RejectInvitationDto) {
    return this.invitationService.rejectInvitation(
      rejectInvitationDto.invitationId,
    );
  }

  @Public()
  @Post('accept')
  async acceptInvitation(@Body() acceptInvitationDto: AcceptInvitationDto) {
    const { token, ...userData } = acceptInvitationDto;
    return this.invitationService.acceptInvitation(token, userData);
  }

  @Public()
  @Get('check-token/:token')
  async checkToken(@Param('token') token: string) {
    return this.invitationService.getInvitationByToken(token);
  }

  @Roles(['ADMIN'])
  @Post('send-invitation')
  async sendInvitation(@Body() sendInvitationEmailDto: SendInvitationEmailDto) {
    return this.invitationService.sendInvitationEmail(
      sendInvitationEmailDto.email,
      sendInvitationEmailDto.token,
    );
  }

  @Roles(['ADMIN'])
  @Get('list')
  async listInvitations() {
    return this.invitationService.listInvitations();
  }
}
