import {
  IsS<PERSON>,
  <PERSON>NotEmpty,
  <PERSON>E<PERSON>,
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Length,
  IsUUID,
} from 'class-validator';
import { GenerationType } from 'generated/prisma';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateConversationalGenerationDto {
  @ApiProperty({
    description: 'User-friendly name for the generation',
    example: 'Dashboard Component',
    minLength: 1,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'Type of generation (UI or DOCUMENTATION)',
    enum: GenerationType,
    example: GenerationType.UI,
  })
  @IsEnum(GenerationType)
  type: GenerationType;

  @ApiProperty({
    description: 'Initial prompt to start the conversation',
    example: 'Create a responsive dashboard component with charts and metrics',
    minLength: 10,
    maxLength: 2000,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(10)
  @MaxLength(2000)
  initialPrompt: string;

  @ApiProperty({
    description: 'Project ID where this generation belongs',
    format: 'uuid',
  })
  @IsNotEmpty()
  projectId: string;

  @ApiPropertyOptional({
    description: 'Optional prompt history ID to link with existing prompt',
    format: 'uuid',
  })
  @IsOptional()
  promptHistoryId?: string;
}
