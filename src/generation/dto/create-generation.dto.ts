import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>UUI<PERSON>,
} from 'class-validator';
import { GenerationType } from 'generated/prisma';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateGenerationDto {
  @ApiProperty({
    description: 'Type of generation (UI or DOCUMENTATION)',
    enum: GenerationType,
    example: GenerationType.UI,
  })
  @IsEnum(GenerationType)
  type: GenerationType;

  @ApiProperty({
    description: 'Prompt for the generation',
    example: 'Create a responsive card component',
    minLength: 10,
    maxLength: 2000,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(10)
  @MaxLength(2000)
  prompt: string;

  @ApiProperty({
    description: 'Project ID where this generation belongs',
    format: 'uuid',
  })
  @IsUUID('4')
  @IsNotEmpty()
  projectId: string;

  @ApiPropertyOptional({
    description: 'Optional prompt history ID to link with existing prompt',
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID('4')
  promptHistoryId?: string;
}
