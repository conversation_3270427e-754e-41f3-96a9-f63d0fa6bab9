import {
  HttpStatus,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createTransport, Transporter } from 'nodemailer';
import { MAILER_OPTIONS } from './constants/mailer.constant';
import { Options } from 'nodemailer/lib/smtp-connection';
import Mail from 'nodemailer/lib/mailer';
import { Template } from './interfaces/template';

@Injectable()
export class MailerService {
  private readonly logger = new Logger(MailerService.name);
  private transporter: Transporter;
  constructor(
    @Inject(MAILER_OPTIONS) private readonly options: Options,
    private readonly configService: ConfigService,
  ) {
    this.transporter = createTransport(this.options);
  }

  async sendMail({
    to,
    subject,
    template,
  }: {
    to: string;
    subject: string;
    template: Template;
  }) {
    try {
      const mailOptions: Mail.Options = {
        from: this.configService.get<string>('EMAIL_FROM'),
        to,
        subject,
        html: template.html,
      };

      // Type de résultat inconnu
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const result = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent successfully to ${to}`);

      return {
        message: 'Email sent successfully',
        data: {
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
          messageId: result.messageId,
          to,
          subject,
        },
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(`Failed to send email to ${to}: ${error}`);
      throw new InternalServerErrorException('Failed to send email');
    }
  }
}
