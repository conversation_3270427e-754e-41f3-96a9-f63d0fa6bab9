import {
  ConflictException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { AddProjectMemberDto } from './dto/add-project-member.dto';
import { RemoveProjectMemberDto } from './dto/remove-project-member.dto';

@Injectable()
export class ProjectService {
  private readonly logger = new Logger(ProjectService.name);

  constructor(private readonly prismaService: PrismaService) {}

  async createProject(createProjectDto: CreateProjectDto, createdById: string) {
    try {
      const { memberIds, ...projectData } = createProjectDto;

      // Create project with initial data
      const project = await this.prismaService.project.create({
        data: {
          ...projectData,
          createdById,
          members: memberIds?.length
            ? {
                create: memberIds.map((userId) => ({
                  userId,
                })),
              }
            : undefined,
        },
        include: {
          createdBy: {
            select: {
              id: true,
              email: true,
              fullName: true,
            },
          },
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  fullName: true,
                },
              },
            },
          },
          _count: {
            select: {
              generations: true,
              promptHistory: true,
            },
          },
        },
      });

      this.logger.log(
        `Project created successfully: ${project.name} by user ${createdById}`,
      );

      return {
        message: 'Project created successfully',
        data: project,
        statusCode: HttpStatus.CREATED,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to create project: ${(error as Error).message}`,
      );
      if ((error as { code?: string }).code === 'P2002') {
        throw new ConflictException('A project with this name already exists');
      }
      throw new InternalServerErrorException('Failed to create project');
    }
  }

  async getAllProjects(userId: string, userRole: string) {
    try {
      // Admins can see all projects, users only see projects they're members of
      const whereClause =
        userRole === 'ADMIN'
          ? {}
          : {
              members: {
                some: {
                  userId: userId,
                },
              },
            };

      const projects = await this.prismaService.project.findMany({
        where: whereClause,
        include: {
          createdBy: {
            select: {
              id: true,
              email: true,
              fullName: true,
            },
          },
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  fullName: true,
                },
              },
            },
          },
          _count: {
            select: {
              generations: true,
              promptHistory: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return {
        message: 'Projects retrieved successfully',
        data: projects,
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to retrieve projects: ${(error as Error).message}`,
      );
      throw new InternalServerErrorException('Failed to retrieve projects');
    }
  }

  async getProjectById(projectId: string, userId: string, userRole: string) {
    try {
      const project = await this.prismaService.project.findUnique({
        where: { id: projectId },
        include: {
          createdBy: {
            select: {
              id: true,
              email: true,
              fullName: true,
            },
          },
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  fullName: true,
                },
              },
            },
          },
          generations: {
            include: {
              createdBy: {
                select: {
                  id: true,
                  email: true,
                  fullName: true,
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
          promptHistory: {
            include: {
              createdBy: {
                select: {
                  id: true,
                  email: true,
                  fullName: true,
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
          _count: {
            select: {
              generations: true,
              promptHistory: true,
            },
          },
        },
      });

      if (!project) {
        throw new NotFoundException('Project not found');
      }

      // Check if user has access to this project
      if (userRole !== 'ADMIN') {
        const isMember = project.members.some(
          (member) => member.userId === userId,
        );
        if (!isMember) {
          throw new ForbiddenException(
            'You do not have access to this project',
          );
        }
      }

      return {
        message: 'Project retrieved successfully',
        data: project,
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to retrieve project: ${(error as Error).message}`,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to retrieve project');
    }
  }

  async updateProject(
    projectId: string,
    updateProjectDto: UpdateProjectDto,
    userId: string,
    userRole: string,
  ) {
    try {
      // Check if project exists and user has permission
      const existingProject = await this.prismaService.project.findUnique({
        where: { id: projectId },
        include: {
          members: true,
        },
      });

      if (!existingProject) {
        throw new NotFoundException('Project not found');
      }

      // Only admins or project creators can update projects
      if (userRole !== 'ADMIN' && existingProject.createdById !== userId) {
        throw new ForbiddenException(
          'You do not have permission to update this project',
        );
      }

      const updatedProject = await this.prismaService.project.update({
        where: { id: projectId },
        data: updateProjectDto,
        include: {
          createdBy: {
            select: {
              id: true,
              email: true,
              fullName: true,
            },
          },
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  fullName: true,
                },
              },
            },
          },
          _count: {
            select: {
              generations: true,
              promptHistory: true,
            },
          },
        },
      });

      this.logger.log(
        `Project updated successfully: ${updatedProject.name} by user ${userId}`,
      );

      return {
        message: 'Project updated successfully',
        data: updatedProject,
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to update project: ${(error as Error).message}`,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update project');
    }
  }

  async addProjectMembers(
    projectId: string,
    addMembersDto: AddProjectMemberDto,
    userId: string,
    userRole: string,
  ) {
    try {
      // Check if project exists and user has permission
      const existingProject = await this.prismaService.project.findUnique({
        where: { id: projectId },
        include: {
          members: true,
        },
      });

      if (!existingProject) {
        throw new NotFoundException('Project not found');
      }

      // Only admins can add members
      if (userRole !== 'ADMIN') {
        throw new ForbiddenException('Only admins can add project members');
      }

      // Check if users exist
      const users = await this.prismaService.user.findMany({
        where: {
          id: {
            in: addMembersDto.userIds,
          },
        },
      });

      if (users.length !== addMembersDto.userIds.length) {
        throw new NotFoundException('One or more users not found');
      }

      // Check for existing memberships
      const existingMemberships =
        await this.prismaService.projectMember.findMany({
          where: {
            projectId,
            userId: {
              in: addMembersDto.userIds,
            },
          },
        });

      const existingUserIds = existingMemberships.map((m) => m.userId);
      const newUserIds = addMembersDto.userIds.filter(
        (id) => !existingUserIds.includes(id),
      );

      if (newUserIds.length === 0) {
        throw new ConflictException(
          'All users are already members of this project',
        );
      }

      // Add new members
      await this.prismaService.projectMember.createMany({
        data: newUserIds.map((userId) => ({
          projectId,
          userId,
        })),
      });

      // Get updated project with members
      const updatedProject = await this.getProjectById(
        projectId,
        userId,
        userRole,
      );

      this.logger.log(
        `Added ${newUserIds.length} members to project ${projectId}`,
      );

      return {
        message: `Successfully added ${newUserIds.length} member(s) to project`,
        data: updatedProject.data,
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to add project members: ${(error as Error).message}`,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to add project members');
    }
  }

  async removeProjectMembers(
    projectId: string,
    removeMembersDto: RemoveProjectMemberDto,
    userId: string,
    userRole: string,
  ) {
    try {
      // Check if project exists and user has permission
      const existingProject = await this.prismaService.project.findUnique({
        where: { id: projectId },
        include: {
          members: true,
        },
      });

      if (!existingProject) {
        throw new NotFoundException('Project not found');
      }

      // Only admins can remove members
      if (userRole !== 'ADMIN') {
        throw new ForbiddenException('Only admins can remove project members');
      }

      // Check if memberships exist
      const existingMemberships =
        await this.prismaService.projectMember.findMany({
          where: {
            projectId,
            userId: {
              in: removeMembersDto.userIds,
            },
          },
        });

      if (existingMemberships.length === 0) {
        throw new NotFoundException('No matching project memberships found');
      }

      // Remove members
      await this.prismaService.projectMember.deleteMany({
        where: {
          projectId,
          userId: {
            in: removeMembersDto.userIds,
          },
        },
      });

      // Get updated project with members
      const updatedProject = await this.getProjectById(
        projectId,
        userId,
        userRole,
      );

      this.logger.log(
        `Removed ${existingMemberships.length} members from project ${projectId}`,
      );

      return {
        message: `Successfully removed ${existingMemberships.length} member(s) from project`,
        data: updatedProject.data,
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to remove project members: ${(error as Error).message}`,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to remove project members',
      );
    }
  }

  async deleteProject(projectId: string, userId: string, userRole: string) {
    try {
      // Check if project exists and user has permission
      const existingProject = await this.prismaService.project.findUnique({
        where: { id: projectId },
      });

      if (!existingProject) {
        throw new NotFoundException('Project not found');
      }

      // Only admins or project creators can delete projects
      if (userRole !== 'ADMIN' && existingProject.createdById !== userId) {
        throw new ForbiddenException(
          'You do not have permission to delete this project',
        );
      }

      // Delete project (cascade will handle related records)
      await this.prismaService.project.delete({
        where: { id: projectId },
      });

      this.logger.log(
        `Project deleted successfully: ${existingProject.name} by user ${userId}`,
      );

      return {
        message: 'Project deleted successfully',
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to delete project: ${(error as Error).message}`,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to delete project');
    }
  }
}
