import {
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  InferenceClient,
  InferenceClientError,
  InferenceClientProviderApiError,
  InferenceClientProviderOutputError,
  InferenceClientInputError,
} from '@huggingface/inference';
import { GenerationType } from 'generated/prisma';
import {
  AssistantRequest,
  AssistantResponse,
  HuggingFaceConfig,
  HuggingFaceProvider,
} from './interfaces/assistant.interface';

/**
 * AssistantService - Refactored to use official Hugging Face Inference Client API
 *
 * Features:
 * - Modern InferenceClient with proper error handling
 * - Support for text generation and chat completion
 * - Streaming text generation support
 * - Better model defaults for UI and documentation generation
 * - Comprehensive error handling with HF-specific error types
 */
@Injectable()
export class AssistantService {
  private readonly logger = new Logger(AssistantService.name);
  private readonly hf: InferenceClient;
  private readonly config: HuggingFaceConfig;

  constructor(private configService: ConfigService) {
    this.config = {
      apiKey: this.configService.get<string>('HUGGINGFACE_API_KEY') || '',
      baseUrl: this.configService.get<string>('HUGGINGFACE_BASE_URL'),
      defaultModel:
        this.configService.get<string>('HUGGINGFACE_DEFAULT_MODEL') ||
        'mistralai/Mixtral-8x7B-Instruct-v0.1',
      uiModel:
        this.configService.get<string>('HUGGINGFACE_UI_MODEL') ||
        'codellama/CodeLlama-7b-Instruct-hf',
      documentationModel:
        this.configService.get<string>('HUGGINGFACE_DOCS_MODEL') ||
        'mistralai/Mistral-7B-Instruct-v0.2',
      maxTokens: parseInt(
        this.configService.get<string>('HUGGINGFACE_MAX_TOKENS') || '1024',
      ),
      temperature: parseFloat(
        this.configService.get<string>('HUGGINGFACE_TEMPERATURE') || '0.7',
      ),
      defaultProvider: (this.configService.get<string>(
        'HUGGINGFACE_DEFAULT_PROVIDER',
      ) || 'auto') as HuggingFaceProvider,
      uiProvider: (this.configService.get<string>('HUGGINGFACE_UI_PROVIDER') ||
        'together') as HuggingFaceProvider,
      documentationProvider: (this.configService.get<string>(
        'HUGGINGFACE_DOCS_PROVIDER',
      ) || 'fireworks-ai') as HuggingFaceProvider,
    };

    if (!this.config.apiKey) {
      this.logger.warn(
        'HUGGINGFACE_API_KEY not found. Assistant service will use public inference API with rate limits.',
      );
    }

    // Initialize InferenceClient
    this.hf = new InferenceClient(this.config.apiKey || undefined);
  }

  async processRequest(request: AssistantRequest): Promise<AssistantResponse> {
    const startTime = Date.now();

    try {
      this.logger.log(
        `Processing ${request.generationType} generation request`,
      );

      const model = this.getModelForType(request.generationType);

      const conversationContext = this.buildConversationContext(request);

      let response: AssistantResponse;

      if (request.generationType === GenerationType.UI) {
        response = await this.generateUICode(
          conversationContext,
          model,
          startTime,
        );
      } else {
        response = await this.generateDocumentation(
          conversationContext,
          model,
          startTime,
        );
      }

      this.logger.log(
        `Generated ${request.generationType} response in ${response.processingTime}ms`,
      );
      return response;
    } catch (error: any) {
      let errorMessage = 'Unknown error';

      if (error instanceof InferenceClientError) {
        errorMessage = `Inference Client Error: ${error.message}`;
      } else {
        errorMessage = error?.message || 'Unknown error';
      }

      this.logger.error(`Assistant processing failed: ${errorMessage}`, error);
      throw new InternalServerErrorException(
        'Failed to process assistant request',
      );
    }
  }

  private getModelForType(type: GenerationType): string {
    switch (type) {
      case GenerationType.UI:
        return this.config.uiModel!;
      case GenerationType.DOCUMENTATION:
        return this.config.documentationModel!;
      default:
        return this.config.defaultModel!;
    }
  }

  private buildConversationContext(request: AssistantRequest): string {
    let context = '';

    // Add system context based on generation type
    if (request.generationType === GenerationType.UI) {
      context +=
        'You are an expert React/TypeScript developer specializing in creating modern, responsive UI components with Tailwind CSS. ';
      context +=
        'Generate clean, production-ready code with proper TypeScript types and accessibility features.\n\n';
    } else {
      context +=
        'You are a technical documentation expert. Create clear, comprehensive documentation with examples and best practices.\n\n';
    }

    if (request.metadata?.projectContext) {
      context += `Project Context: ${request.metadata.projectContext}\n\n`;
    }

    if (request.conversationHistory.length > 0) {
      context += 'Previous conversation:\n';
      request.conversationHistory.forEach((message) => {
        if (message.role === 'USER') {
          context += `User: ${message.content}\n`;
        } else if (message.role === 'ASSISTANT') {
          context += `Assistant: ${message.content}\n`;
        }
      });
      context += '\n';
    }

    context += `Current request: ${request.userMessage}`;

    return context;
  }

  private async generateUICode(
    context: string,
    model: string,
    startTime: number,
  ): Promise<AssistantResponse> {
    try {
      const prompt = `${context}\n\nGenerate a React TypeScript component with Tailwind CSS. Include:\n1. Proper TypeScript interfaces\n2. Responsive design\n3. Accessibility features\n4. Clean, modern styling\n\nComponent code:`;

      const result = await this.hf.textGeneration({
        model,
        inputs: prompt,
        parameters: {
          max_new_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          return_full_text: false,
          do_sample: true,
          top_p: 0.95,
          repetition_penalty: 1.1,
        },
        provider: this.config.uiProvider as any,
      });

      const generatedCode = result.generated_text || '';

      return {
        content: "Here's your React component:",
        outputData: {
          code: generatedCode,
          metadata: {
            framework: 'React',
            styling: 'Tailwind CSS',
            language: 'TypeScript',
            model: model,
            tokensGenerated: generatedCode.split(' ').length,
          },
        },
        processingTime: Date.now() - startTime,
        model,
      };
    } catch (error: any) {
      let errorMessage = 'Unknown error';
      let errorType = 'UnknownError';

      if (error instanceof InferenceClientProviderApiError) {
        errorMessage = `Provider API Error: ${error.message}`;
        errorType = 'InferenceClientProviderApiError';
      } else if (error instanceof InferenceClientProviderOutputError) {
        errorMessage = `Provider Output Error: ${error.message}`;
        errorType = 'InferenceClientProviderOutputError';
      } else if (error instanceof InferenceClientInputError) {
        errorMessage = `Input Error: ${error.message}`;
        errorType = 'InferenceClientInputError';
      } else if (error instanceof InferenceClientError) {
        errorMessage = `Inference Error: ${error.message}`;
        errorType = 'InferenceClientError';
      } else {
        errorMessage = error?.message || 'Unknown error';
        errorType = error?.constructor?.name || 'UnknownError';
      }

      this.logger.error(`UI generation failed: ${errorMessage}`);

      return {
        content:
          'I apologize, but I encountered an error while generating your UI component. Please try again with a different prompt.',
        outputData: {
          code: '// Error occurred during generation',
          metadata: {
            error: true,
            errorMessage,
            errorType,
          },
        },
        processingTime: Date.now() - startTime,
        model,
      };
    }
  }

  private async generateDocumentation(
    context: string,
    model: string,
    startTime: number,
  ): Promise<AssistantResponse> {
    try {
      const prompt = `${context}\n\nGenerate comprehensive technical documentation. Include:\n1. Clear explanations\n2. Code examples\n3. Best practices\n4. Common pitfalls\n\nDocumentation:`;

      const result = await this.hf.textGeneration({
        model,
        inputs: prompt,
        parameters: {
          max_new_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          return_full_text: false,
          do_sample: true,
          top_p: 0.9,
          repetition_penalty: 1.05,
        },
        provider: this.config.documentationProvider as any,
      });

      const generatedDocs = result.generated_text || '';

      return {
        content: generatedDocs,
        outputData: {
          documentation: generatedDocs,
          metadata: {
            type: 'technical_documentation',
            format: 'markdown',
            model: model,
            tokensGenerated: generatedDocs.split(' ').length,
          },
        },
        processingTime: Date.now() - startTime,
        model,
      };
    } catch (error: any) {
      let errorMessage = 'Unknown error';
      let errorType = 'UnknownError';

      if (error instanceof InferenceClientProviderApiError) {
        errorMessage = `Provider API Error: ${error.message}`;
        errorType = 'InferenceClientProviderApiError';
      } else if (error instanceof InferenceClientProviderOutputError) {
        errorMessage = `Provider Output Error: ${error.message}`;
        errorType = 'InferenceClientProviderOutputError';
      } else if (error instanceof InferenceClientInputError) {
        errorMessage = `Input Error: ${error.message}`;
        errorType = 'InferenceClientInputError';
      } else if (error instanceof InferenceClientError) {
        errorMessage = `Inference Error: ${error.message}`;
        errorType = 'InferenceClientError';
      } else {
        errorMessage = error?.message || 'Unknown error';
        errorType = 'UnknownError';
      }

      this.logger.error(`Documentation generation failed: ${errorMessage}`);

      // Fallback response
      return {
        content:
          'I apologize, but I encountered an error while generating the documentation. Please try again with a different prompt.',
        outputData: {
          documentation:
            '# Error\n\nAn error occurred during documentation generation.',
          metadata: {
            error: true,
            errorMessage,
            errorType,
          },
        },
        processingTime: Date.now() - startTime,
        model,
      };
    }
  }

  async *streamTextGeneration(
    model: string,
    prompt: string,
    parameters?: any,
  ): AsyncGenerator<string, void, unknown> {
    try {
      const stream = this.hf.textGenerationStream({
        model,
        inputs: prompt,
        parameters: {
          max_new_tokens: parameters?.max_new_tokens || this.config.maxTokens,
          temperature: parameters?.temperature || this.config.temperature,
          return_full_text: false,
          do_sample: true,
          ...parameters,
        },
        provider: this.config.defaultProvider as any,
      });

      for await (const chunk of stream) {
        if (chunk.token?.text) {
          yield chunk.token.text;
        }
      }
    } catch (error: any) {
      this.logger.error(
        `Streaming generation failed: ${error?.message || 'Unknown error'}`,
      );
      throw error;
    }
  }

  async chatCompletion(
    messages: Array<{ role: string; content: string }>,
    model?: string,
    options?: {
      max_tokens?: number;
      temperature?: number;
      stream?: boolean;
    },
  ) {
    try {
      const selectedModel = model || this.config.defaultModel!;

      return await this.hf.chatCompletion({
        model: selectedModel,
        messages,
        max_tokens: options?.max_tokens || this.config.maxTokens,
        temperature: options?.temperature || this.config.temperature,
        stream: options?.stream || false,
        provider: this.config.defaultProvider as any,
      });
    } catch (error: any) {
      let errorMessage = 'Unknown error';

      if (error instanceof InferenceClientError) {
        errorMessage = `Chat Completion Error: ${error.message}`;
      } else {
        errorMessage = error?.message || 'Unknown error';
      }

      this.logger.error(`Chat completion failed: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get the appropriate provider for a given generation type
   */
  private getProviderForType(type: GenerationType): string {
    switch (type) {
      case GenerationType.UI:
        return this.config.uiProvider || 'together';
      case GenerationType.DOCUMENTATION:
        return this.config.documentationProvider || 'fireworks-ai';
      default:
        return this.config.defaultProvider || 'auto';
    }
  }

  /**
   * Health check endpoint that includes provider configuration
   */
  healthCheck(): { status: string; models: string[]; config: any } {
    try {
      return {
        status: 'healthy',
        models: [
          this.config.defaultModel!,
          this.config.uiModel!,
          this.config.documentationModel!,
        ],
        config: {
          maxTokens: this.config.maxTokens,
          temperature: this.config.temperature,
          hasApiKey: !!this.config.apiKey,
          providers: {
            default: this.config.defaultProvider,
            ui: this.config.uiProvider,
            documentation: this.config.documentationProvider,
          },
        },
      };
    } catch (error: any) {
      this.logger.error(
        `Health check failed: ${error?.message || 'Unknown error'}`,
      );
      return {
        status: 'unhealthy',
        models: [],
        config: {},
      };
    }
  }
}
