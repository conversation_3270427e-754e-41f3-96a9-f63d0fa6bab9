# WebContainers Integration Guide

## Overview

The assistant service has been optimized for WebContainers integration. The backend focuses on generating high-quality code, while the frontend handles live previews using WebContainers.

## Backend API Response Format

### UI Generation Response
```json
{
  "message": "Message added to conversation successfully",
  "data": {
    "id": "msg_123",
    "role": "ASSISTANT",
    "content": "Here's your React component:",
    "outputData": {
      "code": "import React from 'react';\nimport { FC } from 'react';\n\ninterface DashboardProps {\n  title?: string;\n}\n\nconst Dashboard: FC<DashboardProps> = ({ title = 'Dashboard' }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-100 p-6\">\n      <div className=\"max-w-7xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">{title}</h1>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Metrics</h2>\n            <div className=\"text-3xl font-bold text-blue-600\">1,234</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;",
      "metadata": {
        "framework": "React",
        "styling": "Tailwind CSS",
        "language": "TypeScript",
        "model": "microsoft/CodeGPT-small-py",
        "tokensGenerated": 156
      }
    },
    "processingTime": 1250,
    "model": "microsoft/CodeGPT-small-py"
  },
  "statusCode": 201
}
```

### Documentation Generation Response
```json
{
  "message": "Message added to conversation successfully",
  "data": {
    "id": "msg_124",
    "role": "ASSISTANT",
    "content": "# Dashboard Component Documentation\n\nA responsive dashboard component built with React and Tailwind CSS...",
    "outputData": {
      "documentation": "# Dashboard Component Documentation\n\nA responsive dashboard component built with React and Tailwind CSS...",
      "metadata": {
        "type": "technical_documentation",
        "format": "markdown",
        "model": "microsoft/DialoGPT-medium",
        "tokensGenerated": 89
      }
    },
    "processingTime": 980,
    "model": "microsoft/DialoGPT-medium"
  },
  "statusCode": 201
}
```

## Frontend WebContainers Integration

### 1. Extract Generated Code
```typescript
// After receiving response from backend
const generatedCode = response.data.outputData.code;
const metadata = response.data.outputData.metadata;
```

### 2. WebContainers Setup
```typescript
import { WebContainer } from '@webcontainer/api';

// Initialize WebContainer
const webcontainerInstance = await WebContainer.boot();

// Create file system with generated code
await webcontainerInstance.mount({
  'package.json': {
    file: {
      contents: JSON.stringify({
        name: 'generated-component',
        type: 'module',
        dependencies: {
          'react': '^18.0.0',
          'react-dom': '^18.0.0',
          '@types/react': '^18.0.0',
          'typescript': '^4.9.0'
        }
      })
    }
  },
  'src': {
    directory: {
      'Component.tsx': {
        file: {
          contents: generatedCode
        }
      },
      'App.tsx': {
        file: {
          contents: `
import React from 'react';
import Component from './Component';

function App() {
  return <Component />;
}

export default App;
          `
        }
      }
    }
  }
});
```

### 3. Live Preview
```typescript
// Install dependencies
const installProcess = await webcontainerInstance.spawn('npm', ['install']);
await installProcess.exit;

// Start dev server
const serverProcess = await webcontainerInstance.spawn('npm', ['run', 'dev']);

// Get preview URL
webcontainerInstance.on('server-ready', (port, url) => {
  // Display preview in iframe
  const previewFrame = document.getElementById('preview');
  previewFrame.src = url;
});
```

## Benefits of WebContainers Integration

### 🚀 **Performance**
- **Faster Response**: Backend focuses only on code generation
- **Client-Side Rendering**: Preview generation happens in browser
- **Reduced Server Load**: No server-side preview processing

### 🔒 **Security**
- **Sandboxed Execution**: WebContainers run in isolated environment
- **No Server Risk**: Generated code never executes on server
- **Client-Side Only**: All preview execution happens in browser

### 💡 **User Experience**
- **Real-Time Updates**: Instant preview updates as code changes
- **Interactive Previews**: Users can interact with generated components
- **Hot Reloading**: Automatic refresh on code modifications

### 🛠 **Development**
- **Full Environment**: Complete Node.js environment in browser
- **Package Management**: npm/yarn support for dependencies
- **Build Tools**: Support for bundlers, TypeScript, etc.

## API Endpoints for WebContainers

### Create Conversational Generation
```bash
POST /api/generations/conversational
{
  "name": "Dashboard Component",
  "type": "UI",
  "initialPrompt": "Create a responsive dashboard with charts",
  "projectId": "project-uuid"
}
```

### Continue Conversation
```bash
POST /api/generations/{id}/messages
{
  "content": "Make it darker and add animations",
  "inputData": {
    "currentCode": "...", // Optional: current code state
    "preferences": { "theme": "dark" }
  }
}
```

### Get Conversation History
```bash
GET /api/generations/{id}/conversation?page=1&limit=50
```

## Error Handling

The assistant service provides fallback responses when generation fails:

```json
{
  "content": "I apologize, but I encountered an error while generating your UI component. Please try again with a different prompt.",
  "outputData": {
    "code": "// Error occurred during generation",
    "metadata": {
      "error": true,
      "errorMessage": "Model timeout"
    }
  }
}
```

## Next Steps

1. **Implement WebContainers** in your frontend application
2. **Test with generated code** from the assistant service
3. **Add error handling** for WebContainers failures
4. **Implement code editing** with live preview updates
5. **Add component library** integration for enhanced previews

This integration provides a powerful, secure, and performant way to generate and preview code in real-time!
